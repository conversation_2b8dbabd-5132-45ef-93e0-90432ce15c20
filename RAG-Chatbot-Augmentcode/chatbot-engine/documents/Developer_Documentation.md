## **Developer Documentation: Advanced Legal Chatbot Engine**

**Version:** 1.0
**Date:** July 14, 2025
**Author:** Gemini AI

### **1. Overview & Architecture**

This document details the design and implementation of a sophisticated, Retrieval-Augmented Generation (RAG) chatbot engine tailored for the German legal domain. The engine is built on a hybrid architecture that combines structured knowledge graph principles with advanced search and ranking techniques to provide accurate, context-aware, and citable answers.

The system is split into two primary pipelines:

1.  **The Offline Pipeline (Data Ingestion & Indexing):** This process runs in the background. It crawls source websites, processes documents (HTML, PDF), chunks them, builds a knowledge graph representation, creates embeddings, and stores everything in a specialized database.
2.  **The Online Pipeline (Real-Time Query Engine):** This is the live API that receives a user's query, performs a multi-stage retrieval process to find the most relevant information, and uses a fine-tuned Large Language Model (LLM) to generate a streamed, human-like response with citations.

#### **High-Level Architecture Diagram**

```mermaid
graph TD
    subgraph Offline Pipeline (Background Processing)
        A[Data Sources: Websites URLs, PDFs] -->|Admin Manages| B(Source Manager)
        B --> C{Crawl4AI}
        C --> D[Document Loaders: HTML, PDF]
        D --> E[Chunking & Pre-processing]
        E --> F(Graph Extractor)
        F -->|Entities & Relationships| G[Metadata Enrichment]
        E --> G
        G --> H{Embedding Model: Gemini text-embedding-004}
        H --> I[Store in Milvus: Vectors + Metadata/Graph info]
        H --> J[Store in Keyword Index: BM25]
    end

    subgraph Online Pipeline (Real-Time API)
        K(Frontend Chat App) --> L{API Endpoint: FastAPI}
        L --> M[Hybrid Search]
        M -->|Vector Search| I
        M -->|Keyword Search| J
        M -->|Candidate Chunks| N{Cohere Reranker}
        N -->|Top K Relevant Chunks| O[Prompt Formatter]
        O --> P{Fine-Tuned LLM: Gemini 1.5 Flash*}
        P -->|Streamed Response with Citations| L
        L --> K
    end

    style K fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#bbf,stroke:#333,stroke-width:2px
```

***Note on the Model:** As of this document's date, the specified model is Gemini 1.5 Flash. We assume "2.5" was a typo and will proceed with the current high-performance model. The architecture remains valid for future models.*

### **2. Technology Stack**

| Component | Technology | Role |
| :--- | :--- | :--- |
| **Framework** | LangChain | The core orchestration framework, connecting all components using the LangChain Expression Language (LCEL). |
| **Vector Database** | Milvus | High-performance, scalable database for storing vector embeddings and associated metadata. |
| **Web Crawler** | Crawl4AI | Specialized tool for crawling websites, handling JavaScript rendering and various file types like PDFs. |
| **Chat Model (LLM)**| Gemini 1.5 Flash (Fine-Tuned) | The core generative model for understanding context and generating the final answer. **Fine-tuning is a separate, prerequisite workflow.** |
| **Embedding Model** | Gemini `text-embedding-004` | Creates the numerical vector representations of the text chunks. |
| **Reranker** | Cohere Rerank | A powerful model that re-orders the retrieved chunks for maximum relevance before passing them to the LLM. |
| **API Server** | FastAPI | A modern, high-performance Python web framework for building the API and providing streaming capabilities. |

---

### **3. Part 1: The Offline Pipeline (Data Ingestion & Indexing)**

This pipeline is designed to be run on a schedule or triggered manually by an administrator to keep the knowledge base up-to-date.

#### **3.1. Data Source Management**

An administrator needs to control the data sources. This can be implemented as:
*   A simple JSON or YAML config file (`sources.yaml`).
*   A dedicated table in a relational database (e.g., PostgreSQL).
*   A simple CRUD API endpoint (see Section 5).

**Example `sources.yaml`:**
```yaml
websites:
  - url: "https://www.gesetze-im-internet.de/bgb/"
    crawl_depth: 3
  - url: "https://www.your-law-firm-blog.de/"
    crawl_depth: 2
local_pdfs:
  - path: "/data/legal_commentary_vol1.pdf"
  - path: "/data/landmark_cases_2024.pdf"
```

#### **3.2. Web Crawling with Crawl4AI**

Use `Crawl4AI` to process the source list. It's effective because it can render JavaScript, which is essential for modern websites, and can extract links to PDFs.

**Example Script (`crawl.py`):**```python
import crawl4ai
import yaml

with open("sources.yaml", "r") as f:
    sources = yaml.safe_load(f)

# This will save all content into a specified output folder
# The 'html' and 'pdf' files will be stored for the next step.
for site in sources['websites']:
    crawler = crawl4ai.Crawler(
        urls=[site['url']],
        output_folder="crawled_data",
        crawler_options={"limit": site['crawl_depth']} # Be respectful of servers
    )
    crawler.run()
```

#### **3.3. Document Loading & Chunking (LangChain)**

Once data is crawled, use LangChain's loaders and splitters to prepare it.

```python
from langchain_community.document_loaders import PyPDFLoader, DirectoryLoader, UnstructuredHTMLLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

# Load all crawled HTML files
html_loader = DirectoryLoader('./crawled_data/', glob="**/*.html", loader_cls=UnstructuredHTMLLoader)
# Load all specified PDF files
pdf_loader = DirectoryLoader('./local_pdfs/', glob="**/*.pdf", loader_cls=PyPDFLoader)

html_docs = html_loader.load()
pdf_docs = pdf_loader.load()
all_docs = html_docs + pdf_docs

# Chunking is critical for RAG
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,
    chunk_overlap=200,
    separators=["\n\n", "\n", "§", ".", ",", " "] # Tailor separators to legal text
)
chunks = text_splitter.split_documents(all_docs)
```

#### **3.4. Graph Construction & Metadata Enrichment (The GraphRAG Element)**

This step simulates a knowledge graph by embedding relational information into the metadata of each chunk. This is a pragmatic approach that avoids the complexity of a separate graph database like Neo4j.

1.  **Entity Extraction:** For each chunk, use a powerful model (or a fine-tuned one) to extract key legal entities.
2.  **Metadata Assignment:** Add the extracted entities and the document structure to the chunk's metadata.

**Conceptual Code:**
```python
# This is a conceptual loop. You might use an LLM or a library like spaCy.
# For high quality, consider using a more powerful model like Gemini 1.5 Pro for this offline task.

for chunk in chunks:
    # Source metadata is already present from the loader
    source_url = chunk.metadata.get('source', 'Unknown')
    
    # Simple example of structural metadata
    if ".pdf" in source_url:
        page_num = chunk.metadata.get('page', 0)
        chunk.metadata['full_citation'] = f"{source_url}, page {page_num + 1}"
    else:
        chunk.metadata['full_citation'] = source_url

    # GraphRAG part: Identify relationships
    # This can be a separate LLM call to find connections
    # For now, let's assume we extract paragraph numbers as key entities
    # A real implementation would be more sophisticated (e.g., extracting named laws)
    # extracted_entities = llm.invoke(f"Extract legal references like '§ XXX BGB' from this text: {chunk.page_content}")
    # chunk.metadata['relations'] = extracted_entities
```

#### **3.5. Embedding & Storage (Milvus + Keyword Index)**

Embed the chunks and store them. Milvus handles the vectors, and we'll need a simple way to handle keywords for hybrid search.

```python
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import Milvus
# You will also need a BM25 indexer, e.g., from the 'rank_bm25' library
from rank_bm25 import BM25Okapi
import pickle

# Initialize embedding model
embeddings = GoogleGenerativeAIEmbeddings(model="models/text-embedding-004")

# Store in Milvus
vector_store = Milvus.from_documents(
    documents=chunks,
    embedding=embeddings,
    connection_args={"host": "localhost", "port": "19530"},
    collection_name="legal_docs_v1"
)

# Create and save a BM25 index for keyword search
tokenized_corpus = [doc.page_content.split(" ") for doc in chunks]
bm25 = BM25Okapi(tokenized_corpus)

# Save the BM25 model and the chunks themselves for retrieval
with open("bm25_index.pkl", "wb") as f:
    pickle.dump(bm25, f)
with open("chunks_corpus.pkl", "wb") as f:
    pickle.dump(chunks, f)
```

---

### **4. Part 2: The Online Pipeline (Real-Time API)**

This is the live engine, built with FastAPI and LangChain.

#### **4.1. API Endpoint Definition (FastAPI)**

Create an API that accepts a query and streams the response.

```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from your_rag_chain import get_chain # We will define this next

app = FastAPI()

class QueryRequest(BaseModel):
    query: str
    session_id: str # For conversation history

@app.post("/chat/stream")
async def chat_stream(request: QueryRequest):
    chain = get_chain()
    # The 'astream' method is key for streaming with LCEL
    return StreamingResponse(
        chain.astream({"question": request.query}),
        media_type="text/event-stream"
    )
```

#### **4.2. Hybrid Search Implementation**

Create a custom retriever that combines Milvus (vector) and BM25 (keyword) search results.

```python
from langchain_core.retrievers import BaseRetriever
from langchain_core.documents import Document
from typing import List

class HybridRetriever(BaseRetriever):
    def __init__(self, vector_retriever, bm25_index, corpus):
        super().__init__()
        self.vector_retriever = vector_retriever
        self.bm25_index = bm25_index
        self.corpus = corpus

    def _get_relevant_documents(self, query: str, *, run_manager) -> List[Document]:
        # Vector search
        vector_results = self.vector_retriever.get_relevant_documents(query)

        # Keyword search
        tokenized_query = query.split(" ")
        bm25_scores = self.bm25_index.get_scores(tokenized_query)
        top_n_indices = sorted(range(len(bm25_scores)), key=lambda i: bm25_scores[i], reverse=True)[:10]
        bm25_results = [self.corpus[i] for i in top_n_indices]

        # Combine and deduplicate
        all_results = vector_results + bm25_results
        unique_results = {doc.page_content: doc for doc in all_results}.values()
        
        return list(unique_results)
```

#### **4.3. Full RAG Chain with Reranking (LCEL)**

Now, assemble all the components using LangChain Expression Language.

**File `your_rag_chain.py`:**
```python
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import Milvus
from langchain_cohere import CohereRerank
from langchain.schema.output_parser import StrOutputParser
import pickle

from your_hybrid_retriever import HybridRetriever # The class from 4.2

def format_docs(docs):
    # Formats the reranked documents for the LLM prompt.
    # Includes citation metadata.
    return "\n\n".join(
        f"Source: {doc.metadata.get('full_citation', 'N/A')}\nContent: {doc.page_content}"
        for doc in docs
    )

def get_chain():
    # 1. Initialize models and retrievers
    llm = ChatGoogleGenerativeAI(model="gemini-1.5-flash", temperature=0.1, streaming=True)
    embeddings = GoogleGenerativeAIEmbeddings(model="models/text-embedding-004")
    
    # Load keyword search components
    with open("bm25_index.pkl", "rb") as f:
        bm25_index = pickle.load(f)
    with open("chunks_corpus.pkl", "rb") as f:
        corpus = pickle.load(f)

    # Initialize Milvus retriever
    milvus_store = Milvus(
        embedding_function=embeddings,
        connection_args={"host": "localhost", "port": "19530"},
        collection_name="legal_docs_v1"
    )
    vector_retriever = milvus_store.as_retriever(search_kwargs={"k": 20})

    # Create the Hybrid Retriever
    hybrid_retriever = HybridRetriever(vector_retriever, bm25_index, corpus)

    # 2. Initialize Cohere Reranker
    reranker = CohereRerank(top_n=5) # Will return the top 5 most relevant docs

    # 3. Define the Prompt Template
    template = """
    You are an expert legal assistant for German law students. Answer the user's question based ONLY on the following context.
    If the context does not contain the answer, say so. Be concise, accurate, and use the sources to cite your claims.
    Present the answer first, followed by a "Sources:" list.

    Context:
    {context}

    Question:
    {question}

    Answer:
    """
    prompt = ChatPromptTemplate.from_template(template)

    # 4. Construct the LCEL Chain
    chain = (
        {"context": hybrid_retriever | reranker | format_docs, "question": RunnablePassthrough()}
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return chain
```

### **5. Administrator Interface**

For data management, you can extend the FastAPI app with simple CRUD endpoints. These endpoints would modify the `sources.yaml` file (or a database) and could trigger a script to run the offline indexing pipeline.

**Example Endpoint:**
```python
class NewSourceRequest(BaseModel):
    url: str
    crawl_depth: int

@app.post("/admin/sources/website")
def add_website_source(source: NewSourceRequest):
    # Logic to append this to sources.yaml or a database
    # ...
    return {"status": "success", "message": f"Source {source.url} added."}

@app.post("/admin/reindex")
def trigger_reindex():
    # Logic to trigger the offline pipeline (e.g., using subprocess or a job queue like Celery)
    # ...
    return {"status": "success", "message": "Re-indexing process started."}
```

### **6. Deployment & Scaling**

*   **Containerization:** Use Docker and Docker Compose to containerize your FastAPI application, Milvus instance, and any other dependencies. This ensures consistency across environments.
*   **Milvus Deployment:** For production, use the Milvus operator on Kubernetes for scalability and resilience.
*   **Async Workers:** The FastAPI application should be run with an ASGI server like Uvicorn, using multiple workers to handle concurrent requests.
*   **Job Queue:** For the "re-index" process, which can be time-consuming, use a dedicated job queue system like Celery with Redis or RabbitMQ. This prevents the API from getting blocked.
*   **Monitoring:** Implement logging and monitoring to track API latency, error rates, and the performance of each component in the RAG chain (especially the retriever and reranker steps).

---